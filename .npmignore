# Ignore everything by default
*

# Include only the files specified in package.json "files" section
!index.js
!update-version.js
!setup-workflow.js
!README.md

# Explicitly ignore common development files
node_modules/
.git/
.github/
examples/
test*/
*.test.js
*.spec.js
.env*
.DS_Store
Thumbs.db
*.log
coverage/
.nyc_output/
.vscode/
.idea/
*.swp
*.swo
*~
release.config.js
package-lock.json
yarn.lock
pnpm-lock.yaml
CHANGELOG.md
