{"name": "@clash-strategic/release-config", "version": "0.2.0", "description": "Shared semantic-release config with automatic GitHub Actions workflow generation for Clash Strategic repositories (internal use).", "main": "index.js", "license": "UNLICENSED", "repository": {"type": "git", "url": "https://github.com/ClashStrategic/release-config.git"}, "files": ["index.js", "update-version.js", "setup-workflow.js", "README.md"], "keywords": ["semantic-release", "config", "github-actions", "workflow", "automation", "ci-cd", "clash-strategic"], "scripts": {"semantic-release": "semantic-release", "setup-workflow": "node setup-workflow.js"}, "bin": {"setup-release-workflow": "./setup-workflow.js"}, "devDependencies": {"@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^13.0.1", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^11.0.4", "@semantic-release/npm": "^12.0.2", "@semantic-release/release-notes-generator": "^14.0.3", "semantic-release": "^24.2.7"}}